// 🚀 Elite Trading Bot - Main Application Component
import React, { useState } from 'react'
import { Activity, TrendingUp, DollarSign, BarChart3, Settings, Wifi, WifiOff } from 'lucide-react'
import { Button, Input, Select, Toggle } from './renderer/components/ui'
import { formatCurrency, formatPercentage } from './shared/utils/helpers'
import { TRADE_DURATIONS, MARTINGALE_MODES } from './shared/constants/trading'
import './App.css'

function App() {
	// 🎛️ Trading Configuration State
	const [config, setConfig] = useState({
		tradeCapital: 1000,
		targetProfit: 100,
		tradeAmount: 10,
		tradeDuration: 'M1',
		stopLoss: 50,
		martingaleMode: 'none',
		maxTradesPerSession: 100,
		drawdownLimit: 10
	})

	// 📊 Performance Metrics State
	const [metrics, setMetrics] = useState({
		balance: 1250.0,
		profitLoss: 150.0,
		winRate: 72.5,
		totalTrades: 47,
		activeTrades: 3
	})

	// 🌐 Connection State
	const [isConnected, setIsConnected] = useState(false)
	const [isTrading, setIsTrading] = useState(false)

	const tradeDurationOptions = Object.entries(TRADE_DURATIONS).map(([key, value]) => ({
		value,
		label: value
	}))

	const martingaleModeOptions = Object.entries(MARTINGALE_MODES).map(([key, value]) => ({
		value,
		label: value.charAt(0).toUpperCase() + value.slice(1).replace('-', ' ')
	}))

	return (
		<div className="min-h-screen bg-[#0d1117] text-[#f0f6fc] p-4">
			{/* 🎯 Header */}
			<header className="mb-6">
				<div className="flex items-center justify-between">
					<div className="flex items-center space-x-3">
						<Activity className="h-8 w-8 text-[#2ea043]" />
						<h1 className="text-2xl font-bold">BiBot Elite Trading</h1>
					</div>
					<div className="flex items-center space-x-4">
						<div
							className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
								isConnected ? 'status-connected' : 'status-disconnected'
							}`}
						>
							{isConnected ? <Wifi className="h-4 w-4" /> : <WifiOff className="h-4 w-4" />}
							<span>{isConnected ? 'Connected' : 'Disconnected'}</span>
						</div>
					</div>
				</div>
			</header>

			{/* 📊 Dashboard */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
				<div className="trading-card">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-[#8b949e]">Balance</p>
							<p className="text-xl font-bold text-[#2ea043]">{formatCurrency(metrics.balance)}</p>
						</div>
						<DollarSign className="h-8 w-8 text-[#2ea043]" />
					</div>
				</div>

				<div className="trading-card">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-[#8b949e]">P/L</p>
							<p className={`text-xl font-bold ${metrics.profitLoss >= 0 ? 'text-[#2ea043]' : 'text-[#f85149]'}`}>
								{metrics.profitLoss >= 0 ? '+' : ''}
								{formatCurrency(metrics.profitLoss)}
							</p>
						</div>
						<TrendingUp className={`h-8 w-8 ${metrics.profitLoss >= 0 ? 'text-[#2ea043]' : 'text-[#f85149]'}`} />
					</div>
				</div>

				<div className="trading-card">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-[#8b949e]">Win Rate</p>
							<p className="text-xl font-bold text-[#2ea043]">{formatPercentage(metrics.winRate / 100)}</p>
						</div>
						<BarChart3 className="h-8 w-8 text-[#2ea043]" />
					</div>
				</div>

				<div className="trading-card">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-[#8b949e]">Trades</p>
							<p className="text-xl font-bold">
								{metrics.totalTrades} | Active: {metrics.activeTrades}
							</p>
						</div>
						<Activity className="h-8 w-8 text-[#8b949e]" />
					</div>
				</div>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
				{/* 🎛️ Trading Configuration Panel */}
				<div className="lg:col-span-1">
					<div className="trading-card">
						<div className="flex items-center space-x-2 mb-4">
							<Settings className="h-5 w-5 text-[#2ea043]" />
							<h2 className="text-lg font-semibold">Trading Configuration</h2>
						</div>

						<div className="space-y-4">
							<Input
								label="Trade Capital ($)"
								type="number"
								value={config.tradeCapital}
								onChange={e => setConfig({ ...config, tradeCapital: Number(e.target.value) })}
								className="compact-input"
							/>

							<Input
								label="Target Profit ($)"
								type="number"
								value={config.targetProfit}
								onChange={e => setConfig({ ...config, targetProfit: Number(e.target.value) })}
								className="compact-input"
							/>

							<Input
								label="Trade Amount ($)"
								type="number"
								value={config.tradeAmount}
								onChange={e => setConfig({ ...config, tradeAmount: Number(e.target.value) })}
								className="compact-input"
							/>

							<Select
								label="Trade Duration"
								options={tradeDurationOptions}
								value={config.tradeDuration}
								onChange={value => setConfig({ ...config, tradeDuration: value })}
							/>

							<Select
								label="Martingale Mode"
								options={martingaleModeOptions}
								value={config.martingaleMode}
								onChange={value => setConfig({ ...config, martingaleMode: value })}
							/>

							<Input
								label="Stop Loss ($)"
								type="number"
								value={config.stopLoss}
								onChange={e => setConfig({ ...config, stopLoss: Number(e.target.value) })}
								className="compact-input"
							/>
						</div>
					</div>
				</div>

				{/* 🎮 Trading Control Center */}
				<div className="lg:col-span-2">
					<div className="trading-card">
						<h2 className="text-lg font-semibold mb-4">Trading Control Center</h2>

						{/* Trading Buttons */}
						<div className="grid grid-cols-2 gap-4 mb-6">
							<Button
								variant="buy"
								size="xl"
								className="trade-button h-20 text-xl font-bold"
								disabled={!isConnected || isTrading}
							>
								🟢 BUY / CALL
							</Button>

							<Button
								variant="sell"
								size="xl"
								className="trade-button h-20 text-xl font-bold"
								disabled={!isConnected || isTrading}
							>
								🔴 SELL / PUT
							</Button>
						</div>

						{/* Bot Controls */}
						<div className="flex items-center justify-center space-x-4 mb-6">
							<Button
								variant={isTrading ? 'destructive' : 'default'}
								size="lg"
								onClick={() => setIsTrading(!isTrading)}
								disabled={!isConnected}
								className="min-w-32"
							>
								{isTrading ? '⏹️ STOP BOT' : '▶️ START BOT'}
							</Button>

							<Button variant="outline" size="lg" onClick={() => setIsConnected(!isConnected)} className="min-w-32">
								{isConnected ? '🔌 Disconnect' : '🔗 Connect'}
							</Button>
						</div>

						{/* Quick Settings */}
						<div className="border-t border-[#8b949e] pt-4">
							<h3 className="text-sm font-medium mb-3 text-[#8b949e]">Quick Settings</h3>
							<div className="grid grid-cols-2 gap-4">
								<Toggle
									label="Auto Trading"
									description="Enable automated trading"
									checked={isTrading}
									onChange={setIsTrading}
									disabled={!isConnected}
								/>

								<Toggle label="Sound Alerts" description="Play sound on trades" checked={true} onChange={() => {}} />
							</div>
						</div>

						{/* Live Status */}
						<div className="mt-4 p-3 bg-[#0d1117] rounded-lg">
							<div className="flex items-center justify-between text-sm">
								<span className="text-[#8b949e]">Status:</span>
								<span className={`font-medium ${isTrading ? 'text-[#2ea043]' : 'text-[#8b949e]'}`}>
									{isTrading ? '🟢 Trading Active' : '⏸️ Trading Paused'}
								</span>
							</div>
							<div className="flex items-center justify-between text-sm mt-1">
								<span className="text-[#8b949e]">Next Signal:</span>
								<span className="text-[#ffa500]">⏳ Analyzing...</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* 📝 Recent Activity Log */}
			<div className="mt-6">
				<div className="trading-card">
					<h2 className="text-lg font-semibold mb-4">📝 Recent Activity</h2>
					<div className="space-y-2 max-h-40 overflow-y-auto">
						<div className="flex items-center justify-between text-sm p-2 bg-[#0d1117] rounded">
							<span>✅ Connected to PocketOption API</span>
							<span className="text-[#8b949e]">12:34:56</span>
						</div>
						<div className="flex items-center justify-between text-sm p-2 bg-[#0d1117] rounded">
							<span>📈 Signal generated: EUR/USD CALL (85% confidence)</span>
							<span className="text-[#8b949e]">12:33:21</span>
						</div>
						<div className="flex items-center justify-between text-sm p-2 bg-[#0d1117] rounded">
							<span>💰 Trade closed: +$15.50 profit</span>
							<span className="text-[#8b949e]">12:32:10</span>
						</div>
						<div className="flex items-center justify-between text-sm p-2 bg-[#0d1117] rounded">
							<span>⚠️ High volatility detected on GBP/USD</span>
							<span className="text-[#8b949e]">12:31:45</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}

export default App
