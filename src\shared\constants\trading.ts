// 🎯 Trading Constants for Elite Trading Bot

export const WEBSOCKET_CONFIG = {
  URL: 'wss://demo-api-eu.po.market',
  ORIGIN: 'https://pocketoption.com',
  HEARTBEAT_INTERVAL: 20000, // 20 seconds
  RECONNECT_DELAY: 5000,     // 5 seconds
  MAX_RECONNECT_ATTEMPTS: 10,
} as const;

export const TRADE_DURATIONS = {
  S5: 'S5',
  S15: 'S15', 
  S30: 'S30',
  M1: 'M1',
  M5: 'M5',
  M15: 'M15',
  M30: 'M30',
  H1: 'H1',
} as const;

export const TRADE_ACTIONS = {
  CALL: 'call',
  PUT: 'put',
  BUY: 'BUY',
  SELL: 'SELL',
} as const;

export const MARTINGALE_MODES = {
  NONE: 'none',
  MARTINGALE: 'martingale',
  ANTI_MARTINGALE: 'anti-martingale',
  FIBONACCI: 'fibonacci',
  DALEMBERT: 'da<PERSON><PERSON>',
} as const;

export const ASSET_CATEGORIES = {
  CURRENCY: 'currency',
  CRYPTO: 'crypto',
  COMMODITIES: 'commodities',
  INDICES: 'indices',
  STOCKS: 'stocks',
} as const;

export const TRADING_STRATEGIES = {
  RSI: 'RSI',
  MACD: 'MACD',
  BOLLINGER_BANDS: 'Bollinger Bands',
  ORB: 'Opening Range Breakout',
  MOVING_AVERAGES: 'Moving Averages',
  STOCHASTIC: 'Stochastic Oscillator',
  WILLIAMS_R: 'Williams %R',
  ATR: 'Average True Range',
} as const;

export const INDICATOR_PERIODS = {
  RSI: 14,
  MACD_FAST: 12,
  MACD_SLOW: 26,
  MACD_SIGNAL: 9,
  BOLLINGER_PERIOD: 20,
  BOLLINGER_DEVIATION: 2,
  STOCHASTIC_K: 14,
  STOCHASTIC_D: 3,
  WILLIAMS_R: 14,
  ATR: 14,
  SMA_SHORT: 10,
  SMA_LONG: 20,
  EMA_SHORT: 12,
  EMA_LONG: 26,
} as const;

export const RISK_LEVELS = {
  CONSERVATIVE: {
    maxDrawdown: 5,
    riskPerTrade: 1,
    dailyLossLimit: 10,
  },
  MODERATE: {
    maxDrawdown: 10,
    riskPerTrade: 2,
    dailyLossLimit: 20,
  },
  AGGRESSIVE: {
    maxDrawdown: 20,
    riskPerTrade: 5,
    dailyLossLimit: 40,
  },
} as const;

export const UI_THEMES = {
  DARK: {
    primary: '#2ea043',
    error: '#f85149',
    background: '#30363d',
    surface: '#8b949e',
    text: '#f0f6fc',
    textSecondary: '#8b949e',
  },
} as const;

export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
} as const;

export const LOG_CATEGORIES = {
  TRADING: 'trading',
  SYSTEM: 'system',
  WEBSOCKET: 'websocket',
  DATABASE: 'database',
} as const;

export const PERFORMANCE_TARGETS = {
  UI_RESPONSE_TIME: 100,     // ms
  WEBSOCKET_PROCESSING: 50,  // ms
  MEMORY_FOOTPRINT: 500,     // MB
  UPTIME_RELIABILITY: 95,    // %
} as const;

export const DEFAULT_CONFIG = {
  TRADE_CAPITAL: 1000,
  TARGET_PROFIT: 100,
  TRADE_AMOUNT: 10,
  TRADE_DURATION: TRADE_DURATIONS.M1,
  STOP_LOSS: 50,
  MARTINGALE_MODE: MARTINGALE_MODES.NONE,
  MAX_TRADES_PER_SESSION: 100,
  DRAWDOWN_LIMIT: 10,
} as const;

export const EMOJI_INDICATORS = {
  SUCCESS: '✅',
  ERROR: '❌',
  WARNING: '⚠️',
  TRADE: '📈',
  BUY: '🟢',
  SELL: '🔴',
  PROFIT: '💰',
  LOSS: '💸',
  SIGNAL: '🎯',
  CONNECTED: '🔗',
  DISCONNECTED: '🔌',
  LOADING: '⏳',
  COMPLETED: '🏁',
} as const;

export const WEBSOCKET_EVENTS = {
  // Outgoing
  AUTH: 'auth',
  OPEN_ORDER: 'openOrder',
  CLOSE_ORDER: 'closeOrder',
  SUBSCRIBE_STREAM: 'subscribeStream',
  UNSUBSCRIBE_STREAM: 'unsubscribeStream',
  LOAD_HISTORY_PERIOD: 'loadHistoryPeriod',
  HEARTBEAT: 'ps',
  
  // Incoming
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  SUCCESS_AUTH: 'successauth',
  SUCCESS_OPEN_ORDER: 'successopenOrder',
  SUCCESS_CLOSE_ORDER: 'successcloseOrder',
  SUCCESS_UPDATE_BALANCE: 'successupdateBalance',
  SUCCESS_UPDATE_PENDING: 'successupdatePending',
  UPDATE_STREAM: 'updateStream',
  UPDATE_ASSETS: 'updateAssets',
  UPDATE_CHARTS: 'updateCharts',
  UPDATE_OPENED_DEALS: 'updateOpenedDeals',
  UPDATE_CLOSED_DEALS: 'updateClosedDeals',
  LOAD_HISTORY_PERIOD_FAST: 'loadHistoryPeriodFast',
  UPDATE_HISTORY_NEW_FAST: 'updateHistoryNewFast',
} as const;
