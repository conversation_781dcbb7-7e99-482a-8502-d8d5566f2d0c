// 🌐 WebSocket Interface Definitions for PocketOption Integration

export interface AuthMessage {
  session: string;
  uid: string;
  isDemo: number;
  platform: number;
  isFastHistory: boolean;
}

export interface OpenOrderMessage {
  action: 'call' | 'put';
  amount: number;
  asset: string;
  isDemo: number;
  optionType: number;
  requestId: number;
  time: string;
}

export interface CloseOrderMessage {
  orderId: string;
  requestId: number;
}

export interface UpdateStreamData {
  asset: string;
  timestamp: number;
  price: number;
  direction?: 'up' | 'down';
}

export interface UpdateAssetsData {
  assets: Array<{
    id: string;
    name: string;
    category: string;
    isOTC: boolean;
    isActive: boolean;
    minAmount: number;
    maxAmount: number;
  }>;
}

export interface UpdateChartsData {
  asset: string;
  timeframe: string;
  data: Array<{
    timestamp: number;
    open: number;
    high: number;
    low: number;
    close: number;
    volume?: number;
  }>;
}

export interface UpdateOpenedDealsData {
  deals: Array<{
    id: string;
    asset: string;
    action: string;
    amount: number;
    openTime: number;
    closeTime?: number;
    profit?: number;
    status: string;
  }>;
}

export interface UpdateClosedDealsData {
  deals: Array<{
    id: string;
    asset: string;
    action: string;
    amount: number;
    openTime: number;
    closeTime: number;
    profit: number;
    result: 'win' | 'loss';
  }>;
}

export interface SuccessAuthData {
  balance: number;
  currency: string;
  userId: string;
  isDemo: boolean;
}

export interface SuccessOpenOrderData {
  orderId: string;
  requestId: number;
  asset: string;
  amount: number;
  action: string;
  openTime: number;
}

export interface SuccessCloseOrderData {
  orderId: string;
  requestId: number;
  profit: number;
  result: 'win' | 'loss';
}

export interface SuccessUpdateBalanceData {
  balance: number;
  profit: number;
  currency: string;
}

export interface LoadHistoryPeriodFastData {
  asset: string;
  period: string;
  data: Array<{
    timestamp: number;
    open: number;
    high: number;
    low: number;
    close: number;
    volume?: number;
  }>;
}

export interface UpdateHistoryNewFastData {
  asset: string;
  timestamp: number;
  price: number;
}

// WebSocket Event Types
export type WebSocketIncomingEvents = 
  | 'connect'
  | 'disconnect'
  | 'successauth'
  | 'successopenOrder'
  | 'successcloseOrder'
  | 'successupdateBalance'
  | 'successupdatePending'
  | 'updateStream'
  | 'updateAssets'
  | 'updateCharts'
  | 'updateOpenedDeals'
  | 'updateClosedDeals'
  | 'loadHistoryPeriodFast'
  | 'updateHistoryNewFast';

export type WebSocketOutgoingEvents =
  | 'auth'
  | 'openOrder'
  | 'closeOrder'
  | 'subscribeStream'
  | 'unsubscribeStream'
  | 'loadHistoryPeriod'
  | 'ps'; // heartbeat

export interface WebSocketEventMap {
  // Incoming events
  connect: () => void;
  disconnect: () => void;
  successauth: (data: SuccessAuthData) => void;
  successopenOrder: (data: SuccessOpenOrderData) => void;
  successcloseOrder: (data: SuccessCloseOrderData) => void;
  successupdateBalance: (data: SuccessUpdateBalanceData) => void;
  successupdatePending: (data: any) => void;
  updateStream: (data: UpdateStreamData) => void;
  updateAssets: (data: UpdateAssetsData) => void;
  updateCharts: (data: UpdateChartsData) => void;
  updateOpenedDeals: (data: UpdateOpenedDealsData) => void;
  updateClosedDeals: (data: UpdateClosedDealsData) => void;
  loadHistoryPeriodFast: (data: LoadHistoryPeriodFastData) => void;
  updateHistoryNewFast: (data: UpdateHistoryNewFastData) => void;
}

export interface ConnectionStatus {
  isConnected: boolean;
  isAuthenticated: boolean;
  lastHeartbeat: Date | null;
  reconnectAttempts: number;
  connectionError: string | null;
}
