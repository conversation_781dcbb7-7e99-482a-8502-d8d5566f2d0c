@import 'tailwindcss';

/* 🌙 Elite Trading Bot Dark Theme */
@layer base {
	* {
		border-color: #8b949e;
	}

	body {
		@apply bg-[#0d1117] text-[#f0f6fc];
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
	}

	/* Custom scrollbar */
	::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	::-webkit-scrollbar-track {
		background: #30363d;
	}

	::-webkit-scrollbar-thumb {
		background: #8b949e;
		border-radius: 4px;
	}

	::-webkit-scrollbar-thumb:hover {
		background: #2ea043;
	}
}

@layer components {
	/* Compact input styling */
	.compact-input {
		padding: 0 4px !important;
		height: 32px !important;
	}

	/* Trading button animations */
	.trade-button {
		@apply transform transition-all duration-200 ease-in-out;
	}

	.trade-button:hover {
		@apply scale-105 shadow-lg;
	}

	.trade-button:active {
		@apply scale-95;
	}

	/* Status indicators */
	.status-connected {
		@apply bg-[#2ea043] text-white;
	}

	.status-disconnected {
		@apply bg-[#f85149] text-white;
	}

	.status-pending {
		@apply bg-[#ffa500] text-white;
	}

	/* Card styling */
	.trading-card {
		@apply bg-[#30363d] border border-[#8b949e] rounded-lg p-4 shadow-lg;
	}

	/* Gradient backgrounds */
	.gradient-green {
		background: linear-gradient(135deg, #2ea043 0%, #238636 100%);
	}

	.gradient-red {
		background: linear-gradient(135deg, #f85149 0%, #da3633 100%);
	}
}
