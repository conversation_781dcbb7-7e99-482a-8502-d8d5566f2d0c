// 🎨 Custom Select Component for Elite Trading Bot

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Check } from 'lucide-react';
import { cn } from '../../../shared/utils/helpers';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectProps {
  options: SelectOption[];
  value?: string;
  placeholder?: string;
  label?: string;
  error?: string;
  disabled?: boolean;
  className?: string;
  onChange?: (value: string) => void;
}

const Select = React.forwardRef<HTMLDivElement, SelectProps>(
  ({ options, value, placeholder = 'Select option...', label, error, disabled, className, onChange }, ref) => {
    const [isOpen, setIsOpen] = useState(false);
    const selectRef = useRef<HTMLDivElement>(null);

    const selectedOption = options.find(option => option.value === value);

    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleSelect = (optionValue: string) => {
      if (!disabled) {
        onChange?.(optionValue);
        setIsOpen(false);
      }
    };

    return (
      <div className="w-full">
        {label && (
          <label className="block text-sm font-medium text-[#f0f6fc] mb-1">
            {label}
          </label>
        )}
        <div className="relative" ref={selectRef}>
          <div
            ref={ref}
            className={cn(
              'flex h-10 w-full items-center justify-between rounded-md border border-[#8b949e] bg-[#30363d] px-3 py-2 text-sm text-[#f0f6fc] cursor-pointer focus:outline-none focus:ring-2 focus:ring-[#2ea043] focus:border-transparent',
              disabled && 'cursor-not-allowed opacity-50',
              error && 'border-[#f85149] focus:ring-[#f85149]',
              className
            )}
            onClick={() => !disabled && setIsOpen(!isOpen)}
          >
            <span className={cn(!selectedOption && 'text-[#8b949e]')}>
              {selectedOption ? selectedOption.label : placeholder}
            </span>
            <ChevronDown
              className={cn(
                'h-4 w-4 text-[#8b949e] transition-transform',
                isOpen && 'rotate-180'
              )}
            />
          </div>

          {isOpen && (
            <div className="absolute z-50 w-full mt-1 bg-[#30363d] border border-[#8b949e] rounded-md shadow-lg max-h-60 overflow-auto">
              {options.map((option) => (
                <div
                  key={option.value}
                  className={cn(
                    'flex items-center justify-between px-3 py-2 text-sm cursor-pointer hover:bg-[#8b949e]/20',
                    option.disabled && 'cursor-not-allowed opacity-50',
                    option.value === value && 'bg-[#2ea043]/20 text-[#2ea043]'
                  )}
                  onClick={() => !option.disabled && handleSelect(option.value)}
                >
                  <span className="text-[#f0f6fc]">{option.label}</span>
                  {option.value === value && (
                    <Check className="h-4 w-4 text-[#2ea043]" />
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
        {error && (
          <p className="mt-1 text-sm text-[#f85149]">{error}</p>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

export { Select };
