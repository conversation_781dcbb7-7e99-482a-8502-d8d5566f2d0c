// 🎯 Core Trading Interfaces for Elite Trading Bot

export type TradeDuration = 'S5' | 'S15' | 'S30' | 'M1' | 'M5' | 'M15' | 'M30' | 'H1';
export type TradeAction = 'call' | 'put' | 'BUY' | 'SELL';
export type MartingaleMode = 'none' | 'martingale' | 'anti-martingale' | 'fibonacci' | 'dalembert';
export type AssetCategory = 'currency' | 'crypto' | 'commodities' | 'indices' | 'stocks';

export interface TradingConfig {
  tradeCapital: number;        // Total session capital
  targetProfit: number;        // Take profit threshold
  tradeAmount: number;         // Individual trade size
  tradeDuration: TradeDuration;
  stopLoss: number;           // Hard loss limit
  martingaleMode: MartingaleMode;
  maxTradesPerSession: number;
  drawdownLimit: number;      // % drop from peak
}

export interface AssetInfo {
  id: string;
  name: string;
  category: AssetCategory;
  isOTC: boolean;
  isActive: boolean;
  spread: number;
  volatility: number;
  minAmount: number;
  maxAmount: number;
}

export interface SignalData {
  timestamp: Date;
  asset: string;
  action: TradeAction;
  amount: number;
  strategy: string;
  confidence: number;
  indicators: Record<string, number>;
}

export interface TradeResult {
  id: string;
  sessionId: string;
  asset: string;
  action: TradeAction;
  amount: number;
  entryTime: Date;
  exitTime?: Date;
  entryPrice: number;
  exitPrice?: number;
  result?: number;
  strategy: string;
  isDemo: boolean;
  status: 'pending' | 'active' | 'closed' | 'cancelled';
}

export interface TradingSession {
  id: string;
  startTime: Date;
  endTime?: Date;
  initialBalance: number;
  currentBalance: number;
  finalBalance?: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  maxDrawdown: number;
  isActive: boolean;
}

export interface PerformanceMetrics {
  balance: number;
  profitLoss: number;
  winRate: number;
  totalTrades: number;
  activeTrades: number;
  dailyPnL: number;
  weeklyPnL: number;
  monthlyPnL: number;
  maxDrawdown: number;
  currentDrawdown: number;
}

export interface RiskManagement {
  maxDrawdown: number;
  dailyLossLimit: number;
  emergencyStop: boolean;
  positionSizing: 'fixed' | 'percentage' | 'kelly';
  riskPerTrade: number;
}

export interface TechnicalIndicator {
  name: string;
  value: number;
  signal: 'buy' | 'sell' | 'neutral';
  strength: number; // 0-100
}

export interface MarketData {
  asset: string;
  timestamp: Date;
  price: number;
  volume?: number;
  bid?: number;
  ask?: number;
  spread?: number;
}

export interface OHLC {
  timestamp: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}
