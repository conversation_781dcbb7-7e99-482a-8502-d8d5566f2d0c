{"name": "bibot", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build && electron-builder", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "@types/socket.io-client": "^3.0.0", "@types/sqlite3": "^5.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.515.0", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "sqlite3": "^5.1.7", "tailwindcss": "^4.1.10"}, "devDependencies": {"@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "electron": "^30.0.1", "electron-builder": "^24.13.3", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.1.6", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5"}, "main": "dist-electron/main.js", "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "electron", "esbuild"]}}