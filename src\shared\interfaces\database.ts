// 🗄️ Database Interface Definitions for SQLite Integration

export interface DatabaseSession {
  id: string;
  start_time: string;
  end_time?: string;
  initial_balance: number;
  final_balance?: number;
  total_trades: number;
  winning_trades: number;
  losing_trades: number;
  win_rate: number;
  max_drawdown: number;
  profit_loss: number;
  is_demo: boolean;
  created_at: string;
  updated_at: string;
}

export interface DatabaseTrade {
  id: string;
  session_id: string;
  asset: string;
  action: 'call' | 'put';
  amount: number;
  entry_time: string;
  exit_time?: string;
  entry_price: number;
  exit_price?: number;
  result?: number;
  strategy: string;
  indicators: string; // JSON string
  is_demo: boolean;
  status: 'pending' | 'active' | 'closed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

export interface DatabaseConfig {
  id: string;
  key: string;
  value: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  category: 'trading' | 'ui' | 'risk' | 'system';
  created_at: string;
  updated_at: string;
}

export interface DatabaseAsset {
  id: string;
  name: string;
  category: string;
  is_otc: boolean;
  is_active: boolean;
  min_amount: number;
  max_amount: number;
  spread: number;
  volatility: number;
  last_updated: string;
}

export interface DatabaseSignal {
  id: string;
  asset: string;
  action: 'call' | 'put';
  strategy: string;
  confidence: number;
  indicators: string; // JSON string
  timestamp: string;
  executed: boolean;
  trade_id?: string;
}

export interface DatabasePerformance {
  id: string;
  session_id: string;
  timestamp: string;
  balance: number;
  profit_loss: number;
  drawdown: number;
  win_rate: number;
  total_trades: number;
  active_trades: number;
}

export interface DatabaseLog {
  id: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  category: 'trading' | 'system' | 'websocket' | 'database';
  message: string;
  data?: string; // JSON string
  timestamp: string;
}

// Query interfaces
export interface SessionQuery {
  id?: string;
  isDemo?: boolean;
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
}

export interface TradeQuery {
  sessionId?: string;
  asset?: string;
  action?: 'call' | 'put';
  strategy?: string;
  status?: 'pending' | 'active' | 'closed' | 'cancelled';
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
}

export interface LogQuery {
  level?: 'debug' | 'info' | 'warn' | 'error';
  category?: 'trading' | 'system' | 'websocket' | 'database';
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
}

// Database operations
export interface DatabaseOperations {
  // Sessions
  createSession(session: Omit<DatabaseSession, 'id' | 'created_at' | 'updated_at'>): Promise<string>;
  updateSession(id: string, updates: Partial<DatabaseSession>): Promise<void>;
  getSession(id: string): Promise<DatabaseSession | null>;
  getSessions(query?: SessionQuery): Promise<DatabaseSession[]>;
  deleteSession(id: string): Promise<void>;

  // Trades
  createTrade(trade: Omit<DatabaseTrade, 'id' | 'created_at' | 'updated_at'>): Promise<string>;
  updateTrade(id: string, updates: Partial<DatabaseTrade>): Promise<void>;
  getTrade(id: string): Promise<DatabaseTrade | null>;
  getTrades(query?: TradeQuery): Promise<DatabaseTrade[]>;
  deleteTrade(id: string): Promise<void>;

  // Configuration
  setConfig(key: string, value: any, category?: string): Promise<void>;
  getConfig(key: string): Promise<any>;
  getAllConfig(category?: string): Promise<DatabaseConfig[]>;
  deleteConfig(key: string): Promise<void>;

  // Assets
  upsertAsset(asset: DatabaseAsset): Promise<void>;
  getAsset(id: string): Promise<DatabaseAsset | null>;
  getAssets(category?: string): Promise<DatabaseAsset[]>;
  updateAssetStatus(id: string, isActive: boolean): Promise<void>;

  // Signals
  createSignal(signal: Omit<DatabaseSignal, 'id'>): Promise<string>;
  getSignals(asset?: string, limit?: number): Promise<DatabaseSignal[]>;
  markSignalExecuted(id: string, tradeId: string): Promise<void>;

  // Performance
  recordPerformance(performance: Omit<DatabasePerformance, 'id'>): Promise<void>;
  getPerformanceHistory(sessionId: string): Promise<DatabasePerformance[]>;

  // Logs
  createLog(log: Omit<DatabaseLog, 'id'>): Promise<void>;
  getLogs(query?: LogQuery): Promise<DatabaseLog[]>;
  clearOldLogs(daysToKeep: number): Promise<void>;
}
